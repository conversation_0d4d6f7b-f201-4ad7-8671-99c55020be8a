#!/usr/bin/env python3
"""
增强的图分析功能 - 集成版
包含：异常检测、智能知识补全、个性化推荐、知识图谱合并
充分发挥 Qdrant + Neo4j 的强大能力
"""

import asyncio
from typing import List, Dict, Any, Tuple, Set
import numpy as np
import logging
from datetime import datetime, timedelta
from collections import defaultdict
from app import get_memory_client

logger = logging.getLogger(__name__)


class EnhancedGraphAnalytics:
    """增强的图分析功能"""
    
    def __init__(self):
        self.memory = None
    
    async def initialize(self):
        """初始化内存客户端"""
        self.memory = await get_memory_client()
    
    async def analyze_entity_importance(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        分析实体重要性 - 基于连接数、提及频率等
        """
        if not self.memory or not hasattr(self.memory, 'graph'):
            return []
        
        try:
            # 分析实体的连接度和重要性
            cypher = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})
            OPTIONAL MATCH (n)-[r]-(connected)
            WITH n, count(r) AS connection_count, 
                 coalesce(n.mentions, 1) AS mention_count
            RETURN n.name AS entity_name,
                   n.type AS entity_type,
                   connection_count,
                   mention_count,
                   (connection_count * mention_count) AS importance_score
            ORDER BY importance_score DESC
            LIMIT $limit
            """
            
            params = {"user_id": user_id, "limit": limit}
            results = self.memory.graph.graph.query(cypher, params=params)
            
            importance_analysis = []
            for result in results:
                importance_analysis.append({
                    "entity_name": result.get("entity_name"),
                    "entity_type": result.get("entity_type"),
                    "connections": result.get("connection_count", 0),
                    "mentions": result.get("mention_count", 0),
                    "importance_score": result.get("importance_score", 0)
                })
            
            return importance_analysis
            
        except Exception as e:
            print(f"实体重要性分析失败: {e}")
            return []
    
    async def find_entity_communities(self, user_id: str) -> List[Dict[str, Any]]:
        """
        发现实体社区 - 找出紧密相关的实体群组
        """
        if not self.memory or not hasattr(self.memory, 'graph'):
            return []
        
        try:
            # 使用连通性分析找出实体群组
            cypher = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})-[r]-(m:__Entity__ {{user_id: $user_id}})
            WITH n, m, count(r) AS connection_strength
            WHERE connection_strength > 0
            RETURN n.name AS entity1, 
                   m.name AS entity2,
                   connection_strength,
                   n.type AS type1,
                   m.type AS type2
            ORDER BY connection_strength DESC
            """
            
            params = {"user_id": user_id}
            results = self.memory.graph.graph.query(cypher, params=params)
            
            # 构建社区图
            communities = {}
            for result in results:
                entity1 = result.get("entity1")
                entity2 = result.get("entity2")
                strength = result.get("connection_strength", 0)
                
                # 简单的社区检测算法
                community_key = f"{result.get('type1', 'UNKNOWN')}-{result.get('type2', 'UNKNOWN')}"
                if community_key not in communities:
                    communities[community_key] = {
                        "community_type": community_key,
                        "entities": set(),
                        "total_connections": 0
                    }
                
                communities[community_key]["entities"].add(entity1)
                communities[community_key]["entities"].add(entity2)
                communities[community_key]["total_connections"] += strength
            
            # 转换为列表格式
            community_list = []
            for community_type, data in communities.items():
                community_list.append({
                    "community_type": community_type,
                    "entities": list(data["entities"]),
                    "entity_count": len(data["entities"]),
                    "total_connections": data["total_connections"]
                })
            
            return sorted(community_list, key=lambda x: x["total_connections"], reverse=True)
            
        except Exception as e:
            print(f"实体社区发现失败: {e}")
            return []
    
    async def find_shortest_path(self, user_id: str, entity1: str, entity2: str) -> Dict[str, Any]:
        """
        找出两个实体之间的最短路径
        """
        if not self.memory or not hasattr(self.memory, 'graph'):
            return {}
        
        try:
            cypher = f"""
            MATCH (start:__Entity__ {{name: $entity1, user_id: $user_id}}),
                  (end:__Entity__ {{name: $entity2, user_id: $user_id}})
            MATCH path = shortestPath((start)-[*..6]-(end))
            RETURN [node in nodes(path) | node.name] AS path_nodes,
                   [rel in relationships(path) | type(rel)] AS path_relations,
                   length(path) AS path_length
            LIMIT 1
            """
            
            params = {
                "entity1": entity1,
                "entity2": entity2,
                "user_id": user_id
            }
            
            results = self.memory.graph.graph.query(cypher, params=params)
            
            if results:
                result = results[0]
                return {
                    "start_entity": entity1,
                    "end_entity": entity2,
                    "path_nodes": result.get("path_nodes", []),
                    "path_relations": result.get("path_relations", []),
                    "path_length": result.get("path_length", 0),
                    "path_found": True
                }
            else:
                return {
                    "start_entity": entity1,
                    "end_entity": entity2,
                    "path_found": False,
                    "message": "未找到连接路径"
                }
                
        except Exception as e:
            print(f"最短路径查找失败: {e}")
            return {"error": str(e)}
    
    async def get_entity_recommendations(self, user_id: str, entity_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        基于实体关系的智能推荐
        """
        if not self.memory or not hasattr(self.memory, 'graph'):
            return []
        
        try:
            # 多层推荐策略
            cypher = f"""
            MATCH (target:__Entity__ {{name: $entity_name, user_id: $user_id}})
            
            // 直接相关的实体 (1跳)
            OPTIONAL MATCH (target)-[r1]-(direct:__Entity__ {{user_id: $user_id}})
            WITH target, collect(DISTINCT {{
                entity: direct.name, 
                relation: type(r1), 
                distance: 1,
                score: coalesce(direct.mentions, 1) * 3
            }}) AS direct_related
            
            // 间接相关的实体 (2跳)
            OPTIONAL MATCH (target)-[*2]-(indirect:__Entity__ {{user_id: $user_id}})
            WHERE indirect <> target
            WITH target, direct_related, collect(DISTINCT {{
                entity: indirect.name,
                relation: "INDIRECT",
                distance: 2,
                score: coalesce(indirect.mentions, 1) * 1
            }}) AS indirect_related
            
            // 合并推荐
            UNWIND (direct_related + indirect_related) AS recommendation
            WHERE recommendation.entity IS NOT NULL 
              AND recommendation.entity <> $entity_name
            
            RETURN recommendation.entity AS recommended_entity,
                   recommendation.relation AS relation_type,
                   recommendation.distance AS distance,
                   recommendation.score AS relevance_score
            ORDER BY relevance_score DESC, distance ASC
            LIMIT $limit
            """
            
            params = {
                "entity_name": entity_name,
                "user_id": user_id,
                "limit": limit
            }
            
            results = self.memory.graph.graph.query(cypher, params=params)
            
            recommendations = []
            for result in results:
                recommendations.append({
                    "recommended_entity": result.get("recommended_entity"),
                    "relation_type": result.get("relation_type"),
                    "distance": result.get("distance"),
                    "relevance_score": result.get("relevance_score", 0),
                    "recommendation_reason": f"通过 {result.get('relation_type')} 关系相关"
                })
            
            return recommendations
            
        except Exception as e:
            print(f"实体推荐失败: {e}")
            return []
    
    async def detect_anomalies_and_quality_issues(self, user_id: str) -> Dict[str, Any]:
        """
        🔍 异常检测与质量控制
        检测知识图谱中的数据质量问题和异常
        """
        if not self.memory or not hasattr(self.memory, 'graph'):
            return {"status": "error", "message": "图数据库未启用"}

        try:
            anomalies = {
                "duplicate_entities": [],
                "orphaned_entities": [],
                "inconsistent_relationships": [],
                "missing_types": [],
                "suspicious_patterns": [],
                "quality_score": 0.0
            }

            # 1. 检测重复实体（名称相似但不完全相同）
            duplicate_query = f"""
            MATCH (n1:__Entity__ {{user_id: $user_id}}), (n2:__Entity__ {{user_id: $user_id}})
            WHERE n1 <> n2
              AND (toLower(n1.name) CONTAINS toLower(n2.name) OR toLower(n2.name) CONTAINS toLower(n1.name))
              AND abs(size(n1.name) - size(n2.name)) <= 3
            RETURN n1.name as name1, n2.name as name2, n1.type as type1, n2.type as type2
            LIMIT 20
            """

            duplicates = self.memory.graph.graph.query(duplicate_query, params={"user_id": user_id})
            for dup in duplicates:
                anomalies["duplicate_entities"].append({
                    "entity1": dup.get("name1"),
                    "entity2": dup.get("name2"),
                    "type1": dup.get("type1"),
                    "type2": dup.get("type2"),
                    "severity": "medium"
                })

            # 2. 检测孤立实体（没有任何关系的实体）
            orphaned_query = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})
            WHERE NOT (n)-[]-()
            RETURN n.name as name, n.type as type
            LIMIT 10
            """

            orphaned = self.memory.graph.graph.query(orphaned_query, params={"user_id": user_id})
            for orph in orphaned:
                anomalies["orphaned_entities"].append({
                    "entity": orph.get("name"),
                    "type": orph.get("type"),
                    "severity": "low"
                })

            # 3. 检测缺失类型的实体
            missing_type_query = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})
            WHERE n.type IS NULL OR n.type = ""
            RETURN n.name as name
            LIMIT 10
            """

            missing_types = self.memory.graph.graph.query(missing_type_query, params={"user_id": user_id})
            for mt in missing_types:
                anomalies["missing_types"].append({
                    "entity": mt.get("name"),
                    "severity": "medium"
                })

            # 4. 检测不一致的关系（同一对实体有多种关系类型）
            inconsistent_query = f"""
            MATCH (a:__Entity__ {{user_id: $user_id}})-[r1]->(b:__Entity__ {{user_id: $user_id}}),
                  (a)-[r2]->(b)
            WHERE type(r1) <> type(r2)
            RETURN a.name as source, b.name as destination,
                   collect(DISTINCT type(r1)) + collect(DISTINCT type(r2)) as relationships
            LIMIT 10
            """

            inconsistent = self.memory.graph.graph.query(inconsistent_query, params={"user_id": user_id})
            for inc in inconsistent:
                anomalies["inconsistent_relationships"].append({
                    "source": inc.get("source"),
                    "destination": inc.get("destination"),
                    "relationships": inc.get("relationships"),
                    "severity": "high"
                })

            # 5. 计算质量分数
            total_issues = (len(anomalies["duplicate_entities"]) * 2 +
                          len(anomalies["orphaned_entities"]) * 1 +
                          len(anomalies["inconsistent_relationships"]) * 3 +
                          len(anomalies["missing_types"]) * 2)

            # 获取总实体数
            total_entities_query = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})
            RETURN count(n) as total
            """
            total_result = self.memory.graph.graph.query(total_entities_query, params={"user_id": user_id})
            total_entities = total_result[0].get("total", 1) if total_result else 1

            # 质量分数 = 1 - (问题数 / 总实体数)，范围 0-1
            quality_score = max(0, 1 - (total_issues / max(total_entities, 1)))
            anomalies["quality_score"] = round(quality_score, 3)

            return {
                "status": "success",
                "user_id": user_id,
                "anomalies": anomalies,
                "total_entities": total_entities,
                "recommendations": self._generate_quality_recommendations(anomalies)
            }

        except Exception as e:
            logger.error(f"异常检测失败: {e}")
            return {"status": "error", "message": str(e)}

    def _generate_quality_recommendations(self, anomalies: Dict) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []

        if anomalies["duplicate_entities"]:
            recommendations.append(f"发现 {len(anomalies['duplicate_entities'])} 个可能重复的实体，建议合并相似实体")

        if anomalies["orphaned_entities"]:
            recommendations.append(f"发现 {len(anomalies['orphaned_entities'])} 个孤立实体，建议添加相关关系")

        if anomalies["missing_types"]:
            recommendations.append(f"发现 {len(anomalies['missing_types'])} 个缺失类型的实体，建议补充实体类型")

        if anomalies["inconsistent_relationships"]:
            recommendations.append(f"发现 {len(anomalies['inconsistent_relationships'])} 个不一致的关系，建议检查关系定义")

        if anomalies["quality_score"] > 0.8:
            recommendations.append("数据质量良好，继续保持")
        elif anomalies["quality_score"] > 0.6:
            recommendations.append("数据质量中等，建议定期清理")
        else:
            recommendations.append("数据质量较差，建议立即进行数据清理")

        return recommendations

    async def analyze_temporal_patterns(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """
        分析时间模式 - 实体和关系的时间演变
        """
        if not self.memory or not hasattr(self.memory, 'graph'):
            return {}
        
        try:
            cypher = f"""
            MATCH (n:__Entity__ {{user_id: $user_id}})-[r]-(m:__Entity__ {{user_id: $user_id}})
            WHERE r.created IS NOT NULL 
              AND r.created > datetime() - duration({{days: $days}})
            
            WITH date(r.created) AS creation_date,
                 count(r) AS daily_relations,
                 collect(DISTINCT n.name) AS entities_involved
            
            RETURN creation_date,
                   daily_relations,
                   size(entities_involved) AS unique_entities
            ORDER BY creation_date DESC
            """
            
            params = {
                "user_id": user_id,
                "days": days
            }
            
            results = self.memory.graph.graph.query(cypher, params=params)
            
            temporal_analysis = {
                "analysis_period_days": days,
                "daily_activity": [],
                "total_relations": 0,
                "most_active_day": None,
                "activity_trend": "stable"
            }
            
            for result in results:
                daily_data = {
                    "date": str(result.get("creation_date")),
                    "relations_created": result.get("daily_relations", 0),
                    "unique_entities": result.get("unique_entities", 0)
                }
                temporal_analysis["daily_activity"].append(daily_data)
                temporal_analysis["total_relations"] += daily_data["relations_created"]
            
            # 找出最活跃的一天
            if temporal_analysis["daily_activity"]:
                most_active = max(temporal_analysis["daily_activity"], 
                                key=lambda x: x["relations_created"])
                temporal_analysis["most_active_day"] = most_active
            
            return temporal_analysis
            
        except Exception as e:
            print(f"时间模式分析失败: {e}")
            return {"error": str(e)}


async def demo_enhanced_analytics():
    """演示增强的图分析功能"""
    analytics = EnhancedGraphAnalytics()
    await analytics.initialize()
    
    user_id = "test_user"
    
    print("🔍 === 增强图分析演示 ===")
    
    # 1. 实体重要性分析
    print("\n1. 📊 实体重要性分析:")
    importance = await analytics.analyze_entity_importance(user_id, limit=10)
    for item in importance[:5]:  # 显示前5个
        print(f"   {item['entity_name']} ({item['entity_type']}) - "
              f"重要性: {item['importance_score']}, "
              f"连接: {item['connections']}, "
              f"提及: {item['mentions']}")
    
    # 2. 实体社区发现
    print("\n2. 🏘️ 实体社区发现:")
    communities = await analytics.find_entity_communities(user_id)
    for community in communities[:3]:  # 显示前3个社区
        print(f"   社区类型: {community['community_type']}")
        print(f"   实体数量: {community['entity_count']}")
        print(f"   实体: {', '.join(community['entities'][:5])}...")  # 显示前5个实体
        print(f"   连接强度: {community['total_connections']}")
        print()
    
    # 3. 最短路径分析
    print("\n3. 🛤️ 最短路径分析 (张三 -> Python):")
    path = await analytics.find_shortest_path(user_id, "张三", "Python")
    if path.get("path_found"):
        print(f"   路径长度: {path['path_length']}")
        print(f"   路径节点: {' -> '.join(path['path_nodes'])}")
        print(f"   关系类型: {' -> '.join(path['path_relations'])}")
    else:
        print(f"   {path.get('message', '未找到路径')}")
    
    # 4. 智能推荐
    print("\n4. 💡 基于张三的智能推荐:")
    recommendations = await analytics.get_entity_recommendations(user_id, "张三", limit=5)
    for rec in recommendations:
        print(f"   推荐: {rec['recommended_entity']} "
              f"(距离: {rec['distance']}, "
              f"相关性: {rec['relevance_score']:.1f}, "
              f"关系: {rec['relation_type']})")
    
    # 5. 时间模式分析
    print("\n5. ⏰ 时间模式分析 (最近30天):")
    temporal = await analytics.analyze_temporal_patterns(user_id, days=30)
    if not temporal.get("error"):
        print(f"   分析周期: {temporal['analysis_period_days']} 天")
        print(f"   总关系数: {temporal['total_relations']}")
        if temporal.get("most_active_day"):
            most_active = temporal["most_active_day"]
            print(f"   最活跃日期: {most_active['date']} "
                  f"({most_active['relations_created']} 个关系)")


if __name__ == "__main__":
    asyncio.run(demo_enhanced_analytics())
