{"mcpServers": {"mem0-openmemory": {"command": "python", "args": ["openmemory/api/main.py"], "cwd": "C:/Users/<USER>/mem0", "env": {"PYTHONPATH": "C:/Users/<USER>/mem0", "OPENAI_API_KEY": "sk-zxcvb1234567890qwertasdfg", "OPENAI_API_URL": "https://gemini-cli-worker-testing.13467879663.workers.dev/v1", "OPENAI_API_MODEL": "gemini-2.5-pro", "API_KEY": "sk-zxcvb1234567890qwertasdfg", "USER": "default_user", "POSTGRES_HOST": "localhost", "POSTGRES_PORT": "5432", "POSTGRES_DB": "openmemory", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "postgres", "POSTGRES_COLLECTION_NAME": "memories", "NEO4J_URI": "neo4j://localhost:7687", "NEO4J_USERNAME": "neo4j", "NEO4J_PASSWORD": "**********"}, "description": "Memory system with vector and graph storage", "capabilities": ["memory_management", "semantic_search", "knowledge_graph", "entity_extraction", "crud_operations"], "version": "1.0.0", "author": "mem0 Team", "license": "MIT"}}}