#!/usr/bin/env python3
"""
独立的OpenMemory MCP服务器
专门为Claude Desktop等MCP客户端设计
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# 设置环境变量
os.environ.setdefault("PYTHONPATH", "C:/Users/<USER>/mem0")
os.environ.setdefault("OPENAI_API_KEY", "1")
os.environ.setdefault("OPENAI_API_URL", "http://127.0.0.1:8001/v1")
os.environ.setdefault("OPENAI_API_MODEL", "gemini-2.5-flash")
os.environ.setdefault("POSTGRES_HOST", "localhost")
os.environ.setdefault("POSTGRES_PORT", "5432")
os.environ.setdefault("POSTGRES_DB", "openmemory")
os.environ.setdefault("POSTGRES_USER", "postgres")
os.environ.setdefault("POSTGRES_PASSWORD", "1513091437")
os.environ.setdefault("NEO4J_URI", "neo4j://localhost:7687")
os.environ.setdefault("NEO4J_USERNAME", "neo4j")
os.environ.setdefault("NEO4J_PASSWORD", "1513091437")
os.environ.setdefault("USER", "default_user")

# 添加项目路径到Python路径
sys.path.insert(0, "C:/Users/<USER>/mem0")

try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
    import mcp.types as types
except ImportError as e:
    print(f"Error importing MCP: {e}", file=sys.stderr)
    print("Please install mcp package: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 导入格式化工具（安全导入）
try:
    from format_utils import format_memory_result
    USE_FORMATTING = True
except ImportError as e:
    logger.warning(f"格式化工具导入失败，使用默认格式: {e}")
    USE_FORMATTING = False

    def format_memory_result(result, operation_type):
        """备用格式化函数"""
        import json
        return json.dumps(result, ensure_ascii=False, indent=2)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建MCP服务器
server = Server("openmemory")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="add_memories",
            description="Add memory with entities",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Memory content"
                    }
                },
                "required": ["text"]
            }
        ),

        Tool(
            name="list_memories",
            description="Get memory statistics",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="delete_all_memories",
            description="Delete all memories",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),



        Tool(
            name="get_entity_relations",
            description="Analyze entity relations",
            inputSchema={
                "type": "object",
                "properties": {
                    "entity_name": {
                        "type": "string",
                        "description": "Entity name"
                    }
                },
                "required": ["entity_name"]
            }
        ),


        Tool(
            name="hybrid_search",
            description="Search memories semantically",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Result limit",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        ),

        Tool(
            name="get_intelligent_recommendations",
            description="Get smart recommendations",
            inputSchema={
                "type": "object",
                "properties": {
                    "type": {
                        "type": "string",
                        "description": "Recommendation type",
                        "enum": ["personalized", "discovery", "real_time"],
                        "default": "personalized"
                    },
                    "context": {
                        "type": "object",
                        "description": "Context info",
                        "default": {}
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Result limit",
                        "default": 10
                    }
                },
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    try:
        # 导入我们的应用函数
        from app import add_memory, get_all_memories, delete_all_memories, get_entity_relations
        
        user_id = "default_user"
        
        if name == "add_memories":
            text = arguments.get("text", "")
            if not text:
                return [TextContent(type="text", text="错误: 缺少记忆内容")]
            
            result = await add_memory(text, user_id=user_id)
            formatted_result = format_memory_result(result, "add")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "search_memory":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]
            
            result = await search_memory(query, user_id=user_id, limit=5)
            formatted_result = format_memory_result(result, "search")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "list_memories":
            result = await get_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "list")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "delete_all_memories":
            result = await delete_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "delete_all")
            return [TextContent(type="text", text=formatted_result)]



        elif name == "get_entity_relations":
            entity_name = arguments.get("entity_name", "")
            if not entity_name:
                return [TextContent(type="text", text="错误: 缺少实体名称")]

            result = await get_entity_relations(entity_name=entity_name, user_id=user_id)
            formatted_result = format_memory_result(result, "entity_relations")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "hybrid_search":
            query = arguments.get("query", "")
            limit = arguments.get("limit", 10)
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]

            # 🔧 移除重复的动态实体提取，直接使用 app.py 的功能
            from app import search_memory
            result = await search_memory(query, user_id=user_id, limit=limit)
            formatted_result = format_memory_result(result, "hybrid_search")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_intelligent_recommendations":
            # 调用 app.py 中的智能推荐实现
            recommendation_type = arguments.get("type", "personalized")
            context = arguments.get("context", {})
            limit = arguments.get("limit", 10)

            try:
                # 导入 app.py 中的智能推荐函数
                from app import get_intelligent_recommendations

                result = await get_intelligent_recommendations(
                    user_id=user_id,
                    recommendation_type=recommendation_type,
                    context=context,
                    limit=limit
                )

                formatted_result = format_memory_result(result, "intelligent_recommendations")
                return [TextContent(type="text", text=formatted_result)]

            except Exception as e:
                return [TextContent(type="text", text=f"智能推荐失败: {str(e)}")]

        else:
            return [TextContent(type="text", text=f"未知工具: {name}")]
    
    except Exception as e:
        logger.error(f"工具调用失败 {name}: {e}")
        return [TextContent(type="text", text=f"工具调用失败: {e}")]

async def main():
    """主函数"""
    logger.info("启动OpenMemory MCP服务器...")
    
    # 检查依赖
    try:
        from app import add_memory
        logger.info("✓ 应用模块导入成功")
    except Exception as e:
        logger.error(f"✗ 应用模块导入失败: {e}")
        return
    
    # 启动服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
