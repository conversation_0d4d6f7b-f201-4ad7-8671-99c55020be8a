#!/usr/bin/env python3
"""
独立的OpenMemory MCP服务器
专门为Claude Desktop等MCP客户端设计
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# 设置环境变量
os.environ.setdefault("PYTHONPATH", "C:/Users/<USER>/mem0")
os.environ.setdefault("OPENAI_API_KEY", "1")
os.environ.setdefault("OPENAI_API_URL", "http://127.0.0.1:8001/v1")
os.environ.setdefault("OPENAI_API_MODEL", "gemini-2.5-flash")
os.environ.setdefault("POSTGRES_HOST", "localhost")
os.environ.setdefault("POSTGRES_PORT", "5432")
os.environ.setdefault("POSTGRES_DB", "openmemory")
os.environ.setdefault("POSTGRES_USER", "postgres")
os.environ.setdefault("POSTGRES_PASSWORD", "1513091437")
os.environ.setdefault("NEO4J_URI", "neo4j://localhost:7687")
os.environ.setdefault("NEO4J_USERNAME", "neo4j")
os.environ.setdefault("NEO4J_PASSWORD", "1513091437")
os.environ.setdefault("USER", "default_user")

# 添加项目路径到Python路径
sys.path.insert(0, "C:/Users/<USER>/mem0")

try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
    import mcp.types as types
except ImportError as e:
    print(f"Error importing MCP: {e}", file=sys.stderr)
    print("Please install mcp package: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 导入格式化工具（安全导入）
try:
    from format_utils import format_memory_result
    USE_FORMATTING = True
except ImportError as e:
    logger.warning(f"格式化工具导入失败，使用默认格式: {e}")
    USE_FORMATTING = False

    def format_memory_result(result, operation_type):
        """备用格式化函数"""
        import json
        return json.dumps(result, ensure_ascii=False, indent=2)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建MCP服务器
server = Server("openmemory")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """列出可用的工具"""
    return [
        Tool(
            name="add_memories",
            description="智能添加记忆到知识库，自动提取实体关系并构建知识图谱。适用于保存任何需要长期记忆的信息，如个人偏好、工作经验、学习内容、项目信息等。系统会自动检测相似内容并智能更新，避免重复存储。",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "要添加的记忆内容"
                    }
                },
                "required": ["text"]
            }
        ),

        Tool(
            name="list_memories",
            description="获取记忆库统计信息，包括总记忆数量和时间分布。在记忆数量庞大时只显示统计摘要，不列出具体内容。建议配合搜索工具使用以查找特定记忆。",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="delete_all_memories",
            description="完全清空知识库，删除所有记忆内容。此操作不可逆，请谨慎使用。适用于重置知识库或清理测试数据。",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),



        Tool(
            name="get_entity_relations",
            description="知识图谱关系分析工具。基于图数据库深度分析实体的关系网络结构，专注于发现'谁与谁相关'、'什么连接什么'的结构化关系。返回关系统计、关系路径、最强关系和关联实体列表，不返回具体记忆内容。与hybrid_search形成完美互补：此工具用于关系结构发现，hybrid_search用于记忆内容搜索。适合探索实体关系网络、理解知识结构、发现潜在连接。",
            inputSchema={
                "type": "object",
                "properties": {
                    "entity_name": {
                        "type": "string",
                        "description": "要分析关系网络的实体名称，如人名、项目名、技术名称、概念等"
                    }
                },
                "required": ["entity_name"]
            }
        ),


        Tool(
            name="hybrid_search",
            description="智能记忆内容搜索工具。基于向量数据库的语义理解能力，结合图数据库关系信息，精准搜索相关记忆内容。专注于返回具体的记忆文本内容，支持关键词搜索、概念搜索和模糊匹配。与get_entity_relations形成完美互补：此工具用于记忆内容检索，get_entity_relations用于关系结构分析。这是查找和获取具体记忆信息的主要工具。",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询内容，可以是关键词、概念、问题或实体名称"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回结果数量限制",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        ),

        Tool(
            name="get_intelligent_recommendations",
            description="基于用户行为和上下文获取智能推荐内容。提供个性化推荐、探索发现和实时推荐三种模式，帮助发现相关记忆和潜在有用信息。",
            inputSchema={
                "type": "object",
                "properties": {
                    "type": {
                        "type": "string",
                        "description": "推荐类型：personalized(个性化), discovery(探索发现), real_time(实时推荐)",
                        "enum": ["personalized", "discovery", "real_time"],
                        "default": "personalized"
                    },
                    "context": {
                        "type": "object",
                        "description": "上下文信息，如当前查询、最近查询历史等",
                        "default": {}
                    },
                    "limit": {
                        "type": "integer",
                        "description": "推荐数量限制",
                        "default": 10
                    }
                },
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    try:
        # 导入我们的应用函数
        from app import add_memory, get_all_memories, delete_all_memories, get_entity_relations
        
        user_id = "default_user"
        
        if name == "add_memories":
            text = arguments.get("text", "")
            if not text:
                return [TextContent(type="text", text="错误: 缺少记忆内容")]
            
            result = await add_memory(text, user_id=user_id)
            formatted_result = format_memory_result(result, "add")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "search_memory":
            query = arguments.get("query", "")
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]
            
            result = await search_memory(query, user_id=user_id, limit=5)
            formatted_result = format_memory_result(result, "search")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "list_memories":
            result = await get_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "list")
            return [TextContent(type="text", text=formatted_result)]
        
        elif name == "delete_all_memories":
            result = await delete_all_memories(user_id=user_id)
            formatted_result = format_memory_result(result, "delete_all")
            return [TextContent(type="text", text=formatted_result)]



        elif name == "get_entity_relations":
            entity_name = arguments.get("entity_name", "")
            if not entity_name:
                return [TextContent(type="text", text="错误: 缺少实体名称")]

            result = await get_entity_relations(entity_name=entity_name, user_id=user_id)
            formatted_result = format_memory_result(result, "entity_relations")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "hybrid_search":
            query = arguments.get("query", "")
            limit = arguments.get("limit", 10)
            if not query:
                return [TextContent(type="text", text="错误: 缺少搜索查询")]

            # 🔧 移除重复的动态实体提取，直接使用 app.py 的功能
            from app import search_memory
            result = await search_memory(query, user_id=user_id, limit=limit)
            formatted_result = format_memory_result(result, "hybrid_search")
            return [TextContent(type="text", text=formatted_result)]

        elif name == "get_intelligent_recommendations":
            # 调用 app.py 中的智能推荐实现
            recommendation_type = arguments.get("type", "personalized")
            context = arguments.get("context", {})
            limit = arguments.get("limit", 10)

            try:
                # 导入 app.py 中的智能推荐函数
                from app import get_intelligent_recommendations

                result = await get_intelligent_recommendations(
                    user_id=user_id,
                    recommendation_type=recommendation_type,
                    context=context,
                    limit=limit
                )

                formatted_result = format_memory_result(result, "intelligent_recommendations")
                return [TextContent(type="text", text=formatted_result)]

            except Exception as e:
                return [TextContent(type="text", text=f"智能推荐失败: {str(e)}")]

        else:
            return [TextContent(type="text", text=f"未知工具: {name}")]
    
    except Exception as e:
        logger.error(f"工具调用失败 {name}: {e}")
        return [TextContent(type="text", text=f"工具调用失败: {e}")]

async def main():
    """主函数"""
    logger.info("启动OpenMemory MCP服务器...")
    
    # 检查依赖
    try:
        from app import add_memory
        logger.info("✓ 应用模块导入成功")
    except Exception as e:
        logger.error(f"✗ 应用模块导入失败: {e}")
        return
    
    # 启动服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
