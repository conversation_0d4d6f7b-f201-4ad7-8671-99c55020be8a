# MCP Tools English Optimization Standards

## Objective

Establish comprehensive English optimization standards for all MCP tool descriptions in the mem0 project. Achieve 70% token reduction (from ~3,200 to ~950 tokens) while maintaining complete functionality descriptions and ensuring consistency across all 16 tools.

## Core Principles

### 1. Conciseness Principle
- Remove unnecessary technical implementation details
- Eliminate repetitive concept explanations
- Delete redundant usage scenario descriptions

### 2. Consistency Principle
- Standardize to English descriptions only
- Apply standardized description templates
- Maintain consistent terminology usage

### 3. Functionality Principle
- Accurately convey core tool functionality
- Preserve essential parameter information
- Ensure developers understand tool purpose

## Description Template Standard

### Base Template
```
[Action] + [Object] + [Feature]
```

### Template Components
- **Action**: Primary operation the tool performs (Add, Search, Get, Delete, List, Analyze)
- **Object**: Target object of the operation (memory, memories, relations, recommendations, statistics)
- **Feature**: Key characteristic or advantage (optional, max 2 words when essential)

### Template Examples
- `Add memory to database`
- `Search memory content`
- `Get entity relations`
- `Delete all memories`

## Length Limits

### Tool Description Length
- **Maximum Length**: 40 characters
- **Recommended Length**: 20-35 characters
- **Minimum Length**: 15 characters

### Parameter Description Length
- **Maximum Length**: 20 characters
- **Recommended Length**: 10-15 characters
- **Minimum Length**: 5 characters

## Language Style Standards

### 1. Language Requirements
- **Unified English only**
- No mixed language usage
- Avoid unnecessary technical jargon

### 2. Expression Style
- Use concise verb phrases
- Avoid marketing adjectives ("smart", "intelligent", "advanced")
- No exclamation marks or emphasis symbols
- Use simple present tense

### 3. Standardized Terminology

#### Action Verbs
- **Add**: For creating/storing operations
- **Search**: For query/retrieval operations
- **Get**: For fetching/listing operations
- **Delete**: For removal operations
- **List**: For enumeration operations
- **Analyze**: For analysis/processing operations

#### Object Terms
- **memory/memories**: For individual or multiple memory items
- **relations**: For entity relationship data
- **recommendations**: For suggestion/recommendation data
- **statistics**: For statistical/summary data

#### Feature Qualifiers (use sparingly)
- **semantic**: When semantic search is core feature
- **all**: When operating on complete datasets
- **specific**: When targeting individual items

## Prohibited Content

### 1. Technical Implementation Details
- ❌ "based on vector database semantic understanding"
- ❌ "combines graph database relationship information"
- ❌ "automatically extracts entity relationships and builds knowledge graphs"

### 2. Usage Scenario Descriptions
- ❌ "suitable for saving any long-term memory information"
- ❌ "such as personal preferences, work experience, learning content"
- ❌ "recommend using with search tools"

### 3. Tool Comparison Explanations
- ❌ "forms perfect complement with hybrid_search"
- ❌ "this tool is for relationship structure discovery"
- ❌ "this is the main tool for finding specific memory information"

### 4. Redundant Modifiers
- ❌ "intelligent", "smart", "advanced", "powerful"
- ❌ "deep analysis", "comprehensive", "perfect"
- ❌ "automatically", "seamlessly", "efficiently"

## Standardized Tool Descriptions

### Memory Management Tools

| Tool Name | Current Description | Optimized English | Characters |
|-----------|-------------------|------------------|------------|
| add_memory | 添加记忆到知识库 | Add memory to database | 22 |
| add_memories | 智能添加记忆到知识库，自动提取实体关系... | Add memory with entities | 23 |
| list_memories | 获取记忆库统计信息，包括总记忆数量... | Get memory statistics | 20 |
| get_all_memories | 获取所有记忆 | Get all memories | 16 |
| delete_all_memories | 完全清空知识库，删除所有记忆内容... | Delete all memories | 19 |
| clear_all_memories | 清除所有记忆数据（谨慎使用） | Clear all memories | 18 |
| delete_memory | Delete a specific memory by its ID | Delete specific memory | 21 |

### Search Tools

| Tool Name | Current Description | Optimized English | Characters |
|-----------|-------------------|------------------|------------|
| search_memory | 搜索记忆内容 | Search memory content | 21 |
| search_memory_func | Search through stored memories... | Search stored memories | 21 |
| hybrid_search | 智能记忆内容搜索工具。基于向量数据库... | Search memories semantically | 25 |

### Analysis Tools

| Tool Name | Current Description | Optimized English | Characters |
|-----------|-------------------|------------------|------------|
| get_entity_relations | 知识图谱关系分析工具。基于图数据库... | Analyze entity relations | 22 |
| get_intelligent_recommendations | 基于用户行为和上下文获取智能推荐... | Get smart recommendations | 24 |

### Utility Tools

| Tool Name | Current Description | Optimized English | Characters |
|-----------|-------------------|------------------|------------|
| test_mcp_tool | Test tool to verify MCP system is working | Test MCP system | 15 |

## Parameter Description Standards

### Standardized Parameter Descriptions

| Parameter Name | Standard Description | Characters |
|----------------|---------------------|------------|
| text/content | Memory content | 14 |
| query | Search query | 12 |
| user_id | User ID | 7 |
| limit | Result limit | 12 |
| entity_name | Entity name | 11 |
| memory_id | Memory ID | 9 |
| confirm | Confirmation flag | 17 |
| threshold | Similarity threshold | 20 |
| type | Recommendation type | 19 |
| context | Context information | 19 |
| metadata | Additional data | 15 |

### Parameter Description Principles
- Remove repetitive "for memory isolation" explanations
- Delete "optional metadata information" redundant descriptions
- Use most concise noun phrases
- Avoid technical implementation details
- Focus on parameter purpose, not usage context

## Token Reduction Targets

### File-Specific Targets

| File | Current Tokens | Target Tokens | Reduction |
|------|---------------|---------------|-----------|
| mcp_config.json | ~1,200 | ~350 | 71% |
| mcp_server_standalone.py | ~1,400 | ~420 | 70% |
| openmemory MCP | ~600 | ~180 | 70% |
| **Total** | **~3,200** | **~950** | **70%** |

### Component-Specific Targets

| Component | Current Tokens | Target Tokens | Reduction |
|-----------|---------------|---------------|-----------|
| Tool descriptions | ~2,100 | ~630 | 70% |
| Parameter descriptions | ~800 | ~320 | 60% |
| Resources/Prompts | ~300 | ~30 | 90% |

## Implementation Guidelines

### 1. Optimization Steps
1. Identify tool's core functionality
2. Apply standard English template
3. Check length limits compliance
4. Verify functional accuracy
5. Remove prohibited content
6. Standardize parameter descriptions

### 2. Quality Checklist
- Description accurately conveys functionality ✓
- Length within specified limits ✓
- Language is unified and standard ✓
- Redundant content removed ✓
- Template format applied ✓
- Parameters standardized ✓

### 3. Validation Criteria
- All tool descriptions follow template format
- Token consumption reduced by 70%
- Functional descriptions remain accurate
- Format is unified and consistent
- No prohibited content remains

## Vocabulary Mapping

### Chinese to English Action Mapping
- 添加 → Add
- 搜索 → Search
- 获取 → Get
- 删除 → Delete
- 清除 → Clear
- 列出 → List
- 分析 → Analyze

### Chinese to English Object Mapping
- 记忆 → memory/memories
- 内容 → content
- 统计信息 → statistics
- 实体关系 → entity relations
- 推荐内容 → recommendations
- 数据 → data

### Chinese to English Feature Mapping
- 智能 → smart (use sparingly)
- 语义 → semantic
- 所有 → all
- 特定 → specific

## Maintenance Standards

### 1. New Tool Requirements
- Must follow these English standards
- Description length not exceeding 40 characters
- Use standard template format
- Apply consistent terminology

### 2. Existing Tool Updates
- Maintain standard consistency
- Avoid reintroducing redundant content
- Regular review and optimization

### 3. Standard Updates
- Based on usage feedback optimization
- Maintain backward compatibility
- Update documentation promptly

## Expected Results

Through implementing these standards:
- Reduce MCP tool description token consumption by 70%
- Improve description consistency and readability
- Maintain functional description accuracy
- Provide standardized guidance for future maintenance

These standards serve as the definitive reference for all MCP tool description optimization work, ensuring consistent and sustainable optimization results.
