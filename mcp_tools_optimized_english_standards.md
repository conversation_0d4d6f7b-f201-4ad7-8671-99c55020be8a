# MCP Tools Optimized English Standards

## Executive Summary

**Purpose:** Establish definitive English optimization standards for all 16 MCP tools in the mem0 project  
**Target:** Reduce token consumption by 70% (3,200 → 950 tokens)  
**Scope:** 3 configuration files, 16 tools, complete standardization  
**Language:** English only, no exceptions  

## Core Template System

### Standard Template Format
```
[Action] [Object] [Feature]
```

### Action Verbs by Category

| Category | Standard Verbs | Usage |
|----------|---------------|-------|
| Memory Management | Add, Get, Delete, Clear, List | Core CRUD operations |
| Search Operations | Search, Find, Query | Information retrieval |
| Analysis Operations | Analyze, Extract, Process | Data analysis |
| Utility Operations | Test, Verify, Check | System utilities |

### Object Nouns by Category

| Category | Standard Objects | Usage |
|----------|-----------------|-------|
| Memory Management | memory, memories, database | Storage entities |
| Search Operations | content, data, results | Searchable items |
| Analysis Operations | relations, entities, patterns | Analyzable structures |
| Utility Operations | system, service, connection | System components |

### Feature Modifiers (Optional)

| Type | Modifiers | When to Use |
|------|-----------|-------------|
| Method | semantically, specifically, intelligently | When method is key differentiator |
| Scope | all, specific, related | When scope clarification needed |
| Quality | smart, advanced, enhanced | Only when truly distinctive |

## Length Standards

### Tool Descriptions
- **Maximum:** 35 characters
- **Target:** 20-30 characters  
- **Minimum:** 15 characters

### Parameter Descriptions
- **Maximum:** 20 characters
- **Target:** 10-15 characters
- **Minimum:** 8 characters

### Server Descriptions
- **Maximum:** 50 characters
- **Target:** 30-40 characters

## Standardized Tool Descriptions

### Memory Management Tools (10 tools)

| Tool Name | Optimized Description | Length | Template Applied |
|-----------|----------------------|---------|------------------|
| add_memories | Add memory with entities | 23 | Add + memory + with entities |
| list_memories | Get memory statistics | 20 | Get + memory + statistics |
| get_all_memories | Get all memories | 16 | Get + all + memories |
| delete_all_memories | Delete all memories | 19 | Delete + all + memories |
| clear_all_memories | Clear all memories | 18 | Clear + all + memories |
| delete_memory | Delete specific memory | 21 | Delete + specific + memory |
| test_mcp_tool | Test MCP system | 15 | Test + MCP + system |

### Search Tools (3 tools)

| Tool Name | Optimized Description | Length | Template Applied |
|-----------|----------------------|---------|------------------|
| search_memory_func | Search stored memories | 21 | Search + stored + memories |
| hybrid_search | Search memories semantically | 25 | Search + memories + semantically |

### Analysis Tools (2 tools)

| Tool Name | Optimized Description | Length | Template Applied |
|-----------|----------------------|---------|------------------|
| get_entity_relations | Analyze entity relations | 22 | Analyze + entity + relations |
| get_intelligent_recommendations | Get smart recommendations | 24 | Get + smart + recommendations |

## Standardized Parameter Descriptions

### Core Parameters

| Parameter | Standard Description | Length | Usage Context |
|-----------|---------------------|---------|---------------|
| text | Memory content | 14 | Content to store |
| query | Search query | 12 | Search input |
| user_id | User ID | 7 | User identifier |
| limit | Result limit | 12 | Quantity control |
| entity_name | Entity name | 11 | Entity identifier |
| memory_id | Memory ID | 9 | Memory identifier |
| type | Recommendation type | 19 | Type selector |
| context | Context info | 12 | Additional context |

### Extended Parameters

| Parameter | Standard Description | Length | Usage Context |
|-----------|---------------------|---------|---------------|
| confirm | Confirmation flag | 17 | Safety confirmation |
| threshold | Similarity threshold | 20 | Matching criteria |
| filters | Search filters | 14 | Query refinement |
| metadata | Additional data | 15 | Extra information |

## Prohibited Content

### Technical Implementation Details
❌ **Remove:** "based on vector database", "graph database", "semantic understanding"  
✅ **Keep:** Core functionality only

### Usage Scenarios  
❌ **Remove:** "suitable for personal preferences", "work experience", "learning content"  
✅ **Keep:** Essential purpose only

### Tool Comparisons
❌ **Remove:** "complements hybrid_search", "main tool for", "perfect integration"  
✅ **Keep:** Independent descriptions

### Marketing Language
❌ **Remove:** "intelligent", "smart", "automatic", "perfect", "advanced"  
✅ **Keep:** "smart" only when functionally accurate

### Redundant Explanations
❌ **Remove:** "this method is called when", "helps users to", "provides ability to"  
✅ **Keep:** Direct action statements

## File-Specific Applications

### mcp_config.json Optimization
- **Current:** "mem0智能记忆系统 - 支持向量搜索和知识图谱的混合记忆存储"
- **Optimized:** "Memory system with vector and graph storage"
- **Reduction:** 35 → 42 characters (English expansion acceptable for clarity)

### mcp_server_standalone.py Optimization
- **Target:** 6 tools, average 22 characters per description
- **Focus:** Maximum token reduction (currently most verbose)
- **Priority:** Remove all Chinese text and technical details

### openmemory/api/app/mcp_server.py Optimization  
- **Target:** 6 tools, standardize existing English descriptions
- **Focus:** Consistency with other files
- **Priority:** Apply template format to existing concise descriptions

## Quality Assurance Rules

### Mandatory Checks
1. **Language:** 100% English, no Chinese characters
2. **Length:** All descriptions within specified limits
3. **Template:** All descriptions follow [Action] [Object] [Feature] format
4. **Consistency:** Same tools have identical descriptions across files
5. **Accuracy:** Descriptions accurately represent functionality

### Validation Criteria
1. **Functionality Preserved:** Core purpose clearly communicated
2. **Brevity Achieved:** Target length limits met
3. **Consistency Maintained:** Uniform style across all tools
4. **Clarity Ensured:** Developers can understand tool purpose
5. **Standards Followed:** Template format consistently applied

## Implementation Priority

### Phase 1: High Impact (70% token reduction)
1. Convert all Chinese descriptions to English
2. Apply standard template format
3. Remove technical implementation details
4. Eliminate usage scenarios and examples

### Phase 2: Medium Impact (15% additional reduction)
1. Standardize parameter descriptions
2. Remove tool comparison statements
3. Eliminate marketing language

### Phase 3: Fine-tuning (5% additional reduction)
1. Optimize server-level descriptions
2. Streamline capability lists
3. Final consistency review

## Expected Results

### Token Consumption Targets
- **mcp_config.json:** 200 → 60 tokens (70% reduction)
- **mcp_server_standalone.py:** 1,800 → 540 tokens (70% reduction)
- **openmemory/api/app/mcp_server.py:** 1,200 → 350 tokens (71% reduction)
- **Total:** 3,200 → 950 tokens (70% reduction)

### Quality Metrics
- **Consistency:** 100% template compliance
- **Language:** 100% English
- **Accuracy:** 100% functionality preservation
- **Brevity:** Average 22 characters per description

## Maintenance Guidelines

### New Tool Standards
- Must follow established template format
- Description length not exceeding 35 characters
- English only, no exceptions
- Template compliance mandatory

### Update Procedures
- Maintain consistency across all files
- Avoid reintroducing prohibited content
- Regular review and optimization
- Version control for standard updates

This standard serves as the definitive guide for all MCP tool description optimization in the mem0 project.
