# mem0_config.py
# 统一数据库配置 - 直接配置，避免循环依赖
import os
from dotenv import load_dotenv

load_dotenv()  # 从 .env 文件加载环境变量

config = {
    # API版本配置
    "version": "v1.1",

    # 向量存储配置 (Qdrant)
    "vector_store": {
        "provider": "qdrant",
        "config": {
            "host": os.environ.get("QDRANT_HOST", "localhost"),
            "port": int(os.environ.get("QDRANT_PORT", 6333)),
            "collection_name": os.environ.get("QDRANT_COLLECTION", "openmemory_memories"),
            "embedding_model_dims": 1024,  # Qwen3-Embedding-0.6B 模型维度
        },
    },

    # 嵌入模型配置 (Qwen3-Embedding-0.6B via 硅基流动)
    "embedder": {
        "provider": "openai",
        "config": {
            "model": os.environ.get("EMBEDDINGS_MODEL", "Qwen/Qwen3-Embedding-0.6B"),
            "openai_base_url": os.environ.get("EMBEDDINGS_API_URL", "https://api.siliconflow.cn/v1"),
            "api_key": os.environ.get("EMBEDDINGS_API_KEY"),
            "embedding_dims": 1024,  # Qwen3-Embedding-0.6B 模型维度
        },
    },

    # 语言模型配置 (Gemini中转API，OpenAI兼容格式)
    "llm": {
        "provider": "openai",
        "config": {
            "model": os.environ.get("OPENAI_API_MODEL", "gemini-2.5-flash-lite"),
            "openai_base_url": os.environ.get("OPENAI_API_URL", "http://127.0.0.1:8001/v1"),
            "api_key": os.environ.get("OPENAI_API_KEY"),
            "temperature": 0.1,
            "max_tokens": 8000,  # 增加到8000以支持长文本实体提取
            "top_p": 0.1,
        },
    },

    # 图数据库配置 (Neo4j) - 启用实体提取
    "graph_store": {
        "provider": "neo4j",
        "config": {
            "url": os.environ.get("NEO4J_URI", "neo4j://localhost:7687"),
            "username": os.environ.get("NEO4J_USERNAME", "neo4j"),
            "password": os.environ.get("NEO4J_PASSWORD"),
            "base_label": "__Entity__",  # 启用基础标签以改进实体提取
        },
        # 自定义实体提取提示词
        "custom_prompt": """
你是一个专业的实体关系提取专家。请从给定的文本中提取实体和它们之间的关系。

重要指导原则：
1. 提取所有明确提到的人物、地点、组织、项目、技能等实体
2. 建立实体之间的明确关系
3. 对于自我引用（"我"、"我的"等），使用 "USER_ID" 作为实体
4. 关系描述要简洁明确，使用一般现在时
5. 实体名称要保持一致性

实体类型包括：
- PERSON: 人物（如：张三、老李、USER_ID）
- ORGANIZATION: 组织（如：腾讯、阿里巴巴）
- LOCATION: 地点（如：北京、上海、深圳）
- PROJECT: 项目（如：AI项目、微服务架构）
- SKILL: 技能（如：编程、Python开发）
- ROLE: 职位（如：软件工程师、技术经理）

请确保提取的实体和关系准确反映文本内容。
        """,
    },
}