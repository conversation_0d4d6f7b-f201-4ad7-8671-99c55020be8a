"""remove_global_unique_constraint_on_app_name_add_composite_unique

Revision ID: afd00efbd06b
Revises: add_config_table
Create Date: 2025-06-04 01:59:41.637440

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'afd00efbd06b'
down_revision: Union[str, None] = 'add_config_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_apps_name', table_name='apps')
    op.create_index(op.f('ix_apps_name'), 'apps', ['name'], unique=False)
    op.create_index('idx_app_owner_name', 'apps', ['owner_id', 'name'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_app_owner_name', table_name='apps')
    op.drop_index(op.f('ix_apps_name'), table_name='apps')
    op.create_index('ix_apps_name', 'apps', ['name'], unique=True)
    # ### end Alembic commands ###