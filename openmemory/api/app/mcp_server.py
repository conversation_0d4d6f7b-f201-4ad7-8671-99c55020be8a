"""
MCP Server for OpenMemory with resilient memory client handling.

This module implements an MCP (Model Context Protocol) server that provides
memory operations for OpenMemory. The memory client is initialized lazily
to prevent server crashes when external dependencies (like Ollama) are
unavailable. If the memory client cannot be initialized, the server will
continue running with limited functionality and appropriate error messages.

Key features:
- Lazy memory client initialization
- Graceful error handling for unavailable dependencies
- Fallback to database-only mode when vector store is unavailable
- Proper logging for debugging connection issues
- Environment variable parsing for API keys
"""

import contextvars
import datetime
import json
import logging
import uuid
from unidecode import unidecode

# 使用我们项目根目录下的 app.py
import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入我们的 app.py 中的函数
try:
    # 使用 importlib 直接导入项目根目录的 app.py
    import importlib.util
    app_path = os.path.join(project_root, 'app.py')
    spec = importlib.util.spec_from_file_location("root_app", app_path)
    root_app = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(root_app)

    # 获取需要的函数
    add_memory = root_app.add_memory
    search_memory = root_app.search_memory
    get_all_memories = root_app.get_all_memories
    delete_all_memories_func = root_app.delete_all_memories
    delete_memory_func = root_app.delete_memory

    print(f"Successfully imported functions from {app_path}")
except Exception as e:
    print(f"Failed to import from app.py: {e}")
    print(f"Project root: {project_root}")
    print(f"App path: {app_path}")
    raise
from dotenv import load_dotenv
from fastapi import FastAPI, Request
from fastapi.routing import APIRouter
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport
from qdrant_client import models as qdrant_models

# Load environment variables
load_dotenv()

def safe_json_dumps(obj, **kwargs):
    """安全的 JSON 序列化，自动处理 emoji 和特殊字符"""
    def clean_obj(item):
        if isinstance(item, str):
            # 移除或替换 emoji 字符
            return unidecode(item)
        elif isinstance(item, dict):
            return {k: clean_obj(v) for k, v in item.items()}
        elif isinstance(item, list):
            return [clean_obj(i) for i in item]
        else:
            return item

    cleaned_obj = clean_obj(obj)
    return json.dumps(cleaned_obj, ensure_ascii=True, **kwargs)

# Initialize MCP
mcp = FastMCP("mem0-mcp-server")

# 不再需要 get_memory_client_safe 函数，直接使用 app.py 中的函数

# Context variables for user_id and client_name
user_id_var: contextvars.ContextVar[str] = contextvars.ContextVar("user_id")
client_name_var: contextvars.ContextVar[str] = contextvars.ContextVar("client_name")

# Create a router for MCP endpoints
mcp_router = APIRouter(prefix="/mcp")

# Initialize SSE transport
sse = SseServerTransport("/mcp/messages/")

@mcp.tool(description="Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevant information which can be useful in the future conversation. This can also be called when the user asks you to remember something.")
async def add_memories(text: str) -> str:
    uid = user_id_var.get(None)

    if not uid:
        return safe_json_dumps({
            "status": "error",
            "message": "用户ID未提供",
            "id": None
        })

    try:
        # 使用我们的 app.py 中的函数
        result = await add_memory(text, user_id=uid)

        if result and result.get("status") == "success":
            return safe_json_dumps({
                "status": "success",
                "message": "记忆添加成功",
                "result": result.get("result")
            })
        else:
            return safe_json_dumps({
                "status": "error",
                "message": f"添加记忆失败: {result.get('message', '未知错误') if result else '无返回结果'}",
                "id": None
            })


    except Exception as e:
        return safe_json_dumps({
            "status": "error",
            "message": f"添加记忆失败: {str(e)}",
            "id": None
        })


@mcp.tool(description="Search through stored memories. This method is called EVERYTIME the user asks anything.")
async def search_memory_func(query: str) -> str:
    uid = user_id_var.get(None)

    if not uid:
        return safe_json_dumps({
            "status": "error",
            "message": "用户ID未提供",
            "results": []
        })

    try:
        # 使用我们的 app.py 中的函数
        result = await search_memory(query, user_id=uid, limit=5)

        if result and result.get("status") == "success":
            memories = result.get("memories", [])
            return safe_json_dumps({
                "status": "success",
                "message": f"搜索完成，找到 {len(memories)} 个结果",
                "results": memories
            })
        else:
            return safe_json_dumps({
                "status": "error",
                "message": f"搜索失败: {result.get('message', '未知错误') if result else '无返回结果'}",
                "results": []
            })


    except Exception as e:
        return safe_json_dumps({
            "status": "error",
            "message": f"搜索记忆失败: {str(e)}",
            "results": []
        })


@mcp.tool(description="List all memories in the user's memory")
async def list_memories() -> str:
    uid = user_id_var.get(None)

    if not uid:
        return safe_json_dumps({
            "status": "error",
            "message": "用户ID未提供",
            "memories": {"results": [], "relations": []}
        })

    try:
        # 使用我们的 app.py 中的函数
        result = await get_all_memories(user_id=uid)

        if result and result.get("status") == "success":
            memories = result.get("memories", [])
            return safe_json_dumps({
                "status": "success",
                "message": f"获取完成，共 {len(memories)} 条记忆",
                "memories": {
                    "results": memories,
                    "relations": []
                }
            })
        else:
            return safe_json_dumps({
                "status": "error",
                "message": f"获取记忆失败: {result.get('message', '未知错误') if result else '无返回结果'}",
                "memories": {"results": [], "relations": []}
            })

    except Exception as e:
        return safe_json_dumps({
            "status": "error",
            "message": f"获取记忆失败: {str(e)}",
            "memories": {"results": [], "relations": []}
        })


@mcp.tool(description="Delete all memories in the user's memory")
async def delete_all_memories() -> str:
    uid = user_id_var.get(None)

    # 调试信息
    print(f"[DEBUG] delete_all_memories_mcp called with uid: {uid}")

    if not uid:
        error_msg = safe_json_dumps({
            "status": "error",
            "message": "用户ID未提供"
        })
        print(f"[DEBUG] No uid provided, returning: {error_msg}")
        return error_msg

    try:
        print(f"[DEBUG] Calling delete_all_memories with user_id: {uid}")

        # 使用我们的 app.py 中的 delete_all_memories 函数
        result = await delete_all_memories_func(user_id=uid)

        print(f"[DEBUG] delete_all_memories returned: {result}")
        print(f"[DEBUG] Result type: {type(result)}")

        if result and result.get("status") == "success":
            success_msg = safe_json_dumps({
                "status": "success",
                "message": "所有记忆已删除",
                "result": result.get("result")
            })
            print(f"[DEBUG] Returning success: {success_msg}")
            return success_msg
        else:
            error_msg = safe_json_dumps({
                "status": "error",
                "message": f"删除记忆失败: {result.get('message', '未知错误') if result else '无返回结果'}"
            })
            print(f"[DEBUG] Returning error: {error_msg}")
            return error_msg

    except Exception as e:
        error_msg = safe_json_dumps({
            "status": "error",
            "message": f"删除记忆失败: {str(e)}"
        })
        print(f"[DEBUG] Exception occurred: {e}")
        print(f"[DEBUG] Returning exception error: {error_msg}")
        return error_msg


@mcp.tool(description="Delete a specific memory by its ID")
async def delete_memory(memory_id: str) -> str:
    uid = user_id_var.get(None)

    if not uid:
        return safe_json_dumps({
            "status": "error",
            "message": "用户ID未提供"
        })

    if not memory_id:
        return safe_json_dumps({
            "status": "error",
            "message": "记忆ID未提供"
        })

    try:
        # 使用我们的 app.py 中的 delete_memory 函数
        result = await delete_memory_func(memory_id=memory_id, user_id=uid)

        if result and result.get("status") == "success":
            return safe_json_dumps({
                "status": "success",
                "message": f"记忆 {memory_id} 已删除",
                "result": result.get("result")
            })
        else:
            return safe_json_dumps({
                "status": "error",
                "message": f"删除记忆失败: {result.get('message', '未知错误') if result else '无返回结果'}"
            })

    except Exception as e:
        return safe_json_dumps({
            "status": "error",
            "message": f"删除记忆失败: {str(e)}"
        })


@mcp.tool(description="Test tool to verify MCP system is working")
async def test_mcp_tool() -> str:
    """测试 MCP 工具系统是否正常工作"""
    return safe_json_dumps({
        "status": "success",
        "message": "MCP 工具系统正常工作",
        "timestamp": "2025-07-30"
    })


@mcp_router.get("/{client_name}/sse/{user_id}")
async def handle_sse(request: Request):
    """Handle SSE connections for a specific user and client"""
    # Extract user_id and client_name from path parameters
    uid = request.path_params.get("user_id")
    user_token = user_id_var.set(uid or "")
    client_name = request.path_params.get("client_name")
    client_token = client_name_var.set(client_name or "")

    try:
        # Handle SSE connection
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            await mcp._mcp_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    finally:
        # Clean up context variables
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)


@mcp_router.post("/messages/")
async def handle_get_message(request: Request):
    return await handle_post_message(request)


@mcp_router.post("/{client_name}/sse/{user_id}/messages/")
async def handle_post_message(request: Request):
    return await handle_post_message(request)

async def handle_post_message(request: Request):
    """Handle POST messages for SSE"""
    try:
        body = await request.body()

        # Create a simple receive function that returns the body
        async def receive():
            return {"type": "http.request", "body": body, "more_body": False}

        # Create a simple send function that does nothing
        async def send(message):
            return {}

        # Call handle_post_message with the correct arguments
        await sse.handle_post_message(request.scope, receive, send)

        # Return a success response
        return {"status": "ok"}
    finally:
        pass

def setup_mcp_server(app: FastAPI):
    """Setup MCP server with the FastAPI application"""
    mcp._mcp_server.name = "mem0-mcp-server"

    # Include MCP router in the FastAPI app
    app.include_router(mcp_router)
