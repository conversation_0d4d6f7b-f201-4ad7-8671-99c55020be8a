{"mem0": {"version": "v1.1", "vector_store": {"provider": "qdrant", "config": {"collection_name": "openmemory_memories", "host": "localhost", "port": 6333, "embedding_model_dims": 1024}}, "graph_store": {"provider": "neo4j", "config": {"url": "neo4j://localhost:7687", "username": "neo4j", "password": "**********"}}, "llm": {"provider": "openai", "config": {"model": "gemini-2.5-flash-lite", "temperature": 0.1, "max_tokens": 2000, "api_key": "env:OPENAI_API_KEY", "openai_base_url": "http://127.0.0.1:8001/v1"}}, "embedder": {"provider": "openai", "config": {"model": "BAAI/bge-large-zh-v1.5", "api_key": "env:EMBEDDINGS_API_KEY", "openai_base_url": "https://api.siliconflow.cn/v1", "embedding_dims": 1024}}}, "openmemory": {"custom_instructions": "提取以下信息：代码片段、解释说明、相关技术细节、关键特性。重点关注实体关系提取，包括人员、公司、技术、项目之间的关系。"}}