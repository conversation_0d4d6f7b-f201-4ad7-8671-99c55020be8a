absl-py==2.1.0
accelerate==0.12.0
addict==2.4.0
aenum==3.1.15
aiofiles==23.2.1
aiohttp==3.9.3
aiomysql==0.2.0
aiosignal==1.3.1
aiosqlite==0.21.0
alembic==1.16.4
altair==5.2.0
altgraph==0.17.4
annotated-types==0.6.0
anthropic==0.58.2
antlr4-python3-runtime==4.9.3
anyio==4.9.0
appdirs==1.4.4
APScheduler==3.11.0
asgiref==3.9.1
astroid==3.3.11
asttokens==3.0.0
async-timeout==4.0.3
attrs==23.2.0
Authlib==1.6.1
autogen-agentchat==0.5.7
autogen-core==0.5.7
autogen-ext==0.5.7
azure-ai-agents==1.0.2
azure-ai-documentintelligence==1.0.2
azure-ai-inference==1.0.0b9
azure-ai-projects==1.0.0b12
azure-common==1.1.28
azure-core==1.35.0
azure-identity==1.23.1
azure-search-documents==11.5.3
azure-storage-blob==12.26.0
backoff==2.2.1
basicsr==1.4.2
bcrypt==4.3.0
beautifulsoup4==4.12.3
black==25.1.0
blendmodes==2022
blinker==1.9.0
boltons==23.1.1
build==1.2.2.post1
cachetools==5.3.2
certifi==2023.11.17
cffi==1.17.1
chardet==4.0.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.6
chromadb==0.5.23
clean-fid==0.1.29
click==8.2.1
clip==1.0
cobble==0.1.4
cohere==5.16.1
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.2.0
coverage==7.9.2
crewai==0.148.0
crewai-tools==0.55.0
cryptography==45.0.5
cssselect==1.3.0
cycler==0.12.1
cyclopts==3.22.3
databases==0.9.0
dataclasses-json==0.6.7
DataRecorder==3.6.2
decorator==5.2.1
defusedxml==0.7.1
deprecation==2.1.0
dill==0.4.0
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docstring_parser==0.16
docutils==0.21.2
DownloadKit==2.0.7
DrissionPage==*******
duckduckgo_search==8.1.1
durationpy==0.10
einops==0.4.1
email_validator==2.2.0
embedchain==0.1.128
et_xmlfile==2.0.0
exceptiongroup==1.3.0
executing==2.2.0
facexlib==0.3.0
fastapi==0.109.0
fastavro==1.11.1
fastmcp==2.10.6
ffmpy==0.3.1
filelock==3.13.1
filterpy==1.4.5
flatbuffers==25.2.10
font-roboto==0.0.1
fonts==0.0.3
fonttools==4.47.2
frozenlist==1.4.1
fsspec==2023.12.2
ftfy==6.1.3
future==0.18.3
gdown==5.0.1
gfpgan==1.3.8
gitdb==4.0.11
GitPython==3.1.27
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.177.0
google-auth==2.27.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.0
google-genai==1.26.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
gptcache==0.1.44
gradio==3.16.2
greenlet==3.2.3
grpcio==1.73.1
grpcio-status==1.71.2
h11==0.16.0
h2==4.2.0
hpack==4.1.0
html2text==2025.4.15
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.33.4
humanfriendly==10.0
Hypercorn==0.17.3
hyperframe==6.1.0
idna==2.10
imageio==2.33.1
importlib-metadata==7.0.1
importlib_resources==6.5.2
inflection==0.5.1
iniconfig==2.1.0
instructor==1.10.0
ipython==8.37.0
isodate==0.7.2
isort==6.0.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
json5==0.12.0
json_repair==0.25.2
jsonmerge==1.8.0
jsonpatch==1.33
jsonpickle==4.1.1
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.25.0
jsonschema-specifications==2023.12.1
kiwisolver==1.4.5
kornia==0.6.7
kubernetes==33.1.0
lancedb==0.24.1
langchain==0.3.26
langchain-cohere==0.3.5
langchain-community==0.3.27
langchain-core==0.3.69
langchain-experimental==0.3.4
langchain-openai==0.2.14
langchain-text-splitters==0.3.8
langsmith==0.3.45
lark==1.1.2
lazy_loader==0.3
lightning-utilities==0.10.1
linkify-it-py==2.0.2
litellm==1.72.6
llm-templates==0.1.12
llvmlite==0.41.1
lmdb==1.4.1
loguru==0.7.3
lpips==0.1.4
lxml==6.0.0
magika==0.6.2
Mako==1.3.10
mammoth==1.9.1
Markdown==3.5.2
markdown-it-py==3.0.0
markdownify==1.1.0
markitdown==0.1.2
MarkupSafe==2.1.4
marshmallow==3.26.1
matplotlib==3.8.2
matplotlib-inline==0.1.7
mccabe==0.7.0
mcp==1.12.2
mdit-py-plugins==0.4.0
mdurl==0.1.2
mem0ai==0.1.114
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
msal==1.32.3
msal-extensions==1.3.1
multidict==6.0.4
mypy-extensions==1.0.0
neo4j==5.28.1
networkx==3.2.1
nodeenv==1.9.1
numba==0.58.1
numpy==1.26.4
oauthlib==3.2.2
olefile==0.47
omegaconf==2.2.3
onnxruntime==1.22.0
open_clip_torch==2.32.0
openai==1.97.0
openapi-pydantic==0.5.1
opencv-python==********
openpyxl==3.1.5
opentelemetry-api==1.35.0
opentelemetry-exporter-otlp-proto-common==1.35.0
opentelemetry-exporter-otlp-proto-grpc==1.35.0
opentelemetry-exporter-otlp-proto-http==1.35.0
opentelemetry-instrumentation==0.56b0
opentelemetry-instrumentation-asgi==0.56b0
opentelemetry-instrumentation-fastapi==0.56b0
opentelemetry-proto==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
opentelemetry-util-http==0.56b0
orjson==3.11.0
overrides==7.7.0
packaging==23.2
pandas==2.2.0
parso==0.8.4
pathlib==1.0.1
pathspec==0.12.1
pdfminer.six==20250506
pdfplumber==0.11.7
pefile==2023.2.7
piexif==1.1.3
pillow==11.3.0
platformdirs==4.1.0
playwright==1.53.0
pluggy==1.6.0
portalocker==3.2.0
posthog==3.25.0
primp==0.15.0
priority==2.0.0
prompt_toolkit==3.0.51
proto-plus==1.26.1
protobuf==5.29.5
psutil==5.9.8
psycopg==3.2.9
pure_eval==0.2.3
pyarrow==21.0.0
pyasn1==0.5.1
pyasn1-modules==0.3.0
pybase64==1.4.1
pycparser==2.22
pycryptodome==3.20.0
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pyDeprecate==0.3.2
pydub==0.25.1
pyee==13.0.0
Pygments==2.19.2
pyinstaller==6.14.2
pyinstaller-hooks-contrib==2025.6
PyJWT==2.10.1
pylint==3.3.7
PyMySQL==1.1.1
pyparsing==3.1.1
pypdf==5.8.0
pypdfium2==4.30.1
pyperclip==1.9.0
PyPika==0.48.9
pyproject_hooks==1.2.0
pyre-extensions==0.0.23
pyreadline3==3.5.4
pyright==1.1.403
pysbd==0.3.4
PySocks==1.7.1
pytest==8.4.1
pytest-cov==6.2.1
python-dateutil==2.8.2
python-dotenv==1.1.1
python-multipart==0.0.20
python-pptx==1.0.2
pytorch-lightning==2.1.3
pytube==15.0.0
pytz==2025.2
pyvis==0.3.2
PyWavelets==1.5.0
pywin32==310
pywin32-ctypes==0.2.3
PyYAML==6.0.1
qdrant-client==1.15.0
realesrgan==0.3.0
referencing==0.33.0
regex==2024.11.6
requests==2.32.4
requests-file==2.1.0
requests-oauthlib==1.3.1
requests-toolbelt==1.0.0
resize-right==0.0.2
rich==13.9.4
rich-rst==1.3.1
rpds-py==0.17.1
rsa==4.9
ruff==0.12.4
safetensors==0.5.3
schema==0.7.7
scikit-image==0.19.2
scipy==1.12.0
sentencepiece==0.1.99
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.0
socksio==1.0.0
soupsieve==2.5
SpeechRecognition==3.14.3
SQLAlchemy==2.0.41
sqlmodel==0.0.24
sse-starlette==2.4.1
stack-data==0.6.3
starlette==0.35.1
sympy==1.14.0
tabulate==0.9.0
taskgroup==0.2.2
tb-nightly==2.16.0a20240130
tenacity==8.5.0
tensorboard==2.20.0
tensorboard-data-server==0.7.2
tf_keras-nightly==2.16.0.dev2024013010
tifffile==2024.1.30
tiktoken==0.9.0
timm==0.6.7
tldextract==5.3.0
tokenizers==0.20.3
tomli==2.2.1
tomli_w==1.2.0
tomlkit==0.13.3
toolz==0.12.1
torch==1.13.1+cu117
torchdiffeq==0.2.3
torchmetrics==1.3.0.post0
torchsde==0.2.6
torchvision==0.14.1+cu117
tqdm==4.66.1
traitlets==5.14.3
trampoline==0.1.2
transformers==4.53.2
typer==0.16.0
types-requests==2.32.4.20250611
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2023.4
tzlocal==5.3.1
uc-micro-py==1.0.2
uritemplate==4.2.0
urllib3==2.5.0
uv==0.7.20
uvicorn==0.27.0.post1
watchfiles==1.1.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.0.1
win32_setctime==1.2.0
wrapt==1.17.2
wsproto==1.2.0
xformers==0.0.16
xlrd==2.0.2
xlsxwriter==3.2.5
yapf==0.40.2
yarl==1.9.4
youtube-transcript-api==1.0.3
zipp==3.17.0
zstandard==0.23.0
